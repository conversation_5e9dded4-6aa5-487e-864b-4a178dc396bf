1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.linkeye.debug" >
4
5    <uses-sdk
6        android:minSdkVersion="23"
7        android:targetSdkVersion="35" />
8
9    <uses-permission android:name="android.permission.INTERNET" />
9-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:5:5-67
9-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:5:22-64
10
11    <uses-feature
11-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:6:5-87
12        android:name="android.software.leanback"
12-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:6:19-59
13        android:required="false" />
13-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:6:60-84
14    <uses-feature
14-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:7:5-90
15        android:name="android.hardware.touchscreen"
15-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:7:19-62
16        android:required="false" />
16-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:7:63-87
17
18    <application
18-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:9:5-25:19
19        android:allowBackup="true"
19-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:11:9-35
20        android:debuggable="true"
21        android:extractNativeLibs="false"
22        android:hardwareAccelerated="true"
22-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:12:9-43
23        android:label="link-eye"
23-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:10:9-33
24        android:networkSecurityConfig="@xml/network_security_config"
24-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:13:9-69
25        android:testOnly="true"
26        android:usesCleartextTraffic="true" >
26-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:14:9-44
27        <activity
27-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:15:9-24:20
28            android:name="com.example.linkeye.MainActivity"
28-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:16:13-41
29            android:configChanges="keyboardHidden|orientation|screenSize"
29-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:18:13-74
30            android:exported="true" >
30-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:17:13-36
31            <intent-filter>
31-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:19:13-23:29
32                <action android:name="android.intent.action.MAIN" />
32-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:20:17-69
32-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:20:25-66
33
34                <category android:name="android.intent.category.LAUNCHER" />
34-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:21:17-77
34-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:21:27-74
35                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
35-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:22:17-86
35-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:22:27-83
36            </intent-filter>
37        </activity>
38    </application>
39
40</manifest>

-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:2:1-26:12
INJECTED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:2:1-26:12
MERGED from [ijkplayer-cmake-release.aar] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\ca9f848606dba5a7c7d8175588849d6d\transformed\ijkplayer-cmake-release\AndroidManifest.xml:2:1-7:12
	package
		ADDED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:3:5-34
		INJECTED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:5:5-67
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:5:22-64
uses-feature#android.software.leanback
ADDED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:6:5-87
	android:required
		ADDED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:6:60-84
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:6:19-59
uses-feature#android.hardware.touchscreen
ADDED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:7:5-90
	android:required
		ADDED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:7:63-87
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:7:19-62
application
ADDED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:9:5-25:19
INJECTED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:9:5-25:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml
	android:label
		ADDED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:10:9-33
	android:hardwareAccelerated
		ADDED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:12:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:11:9-35
	android:networkSecurityConfig
		ADDED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:13:9-69
	android:usesCleartextTraffic
		ADDED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:14:9-44
activity#com.example.linkeye.MainActivity
ADDED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:15:9-24:20
	android:exported
		ADDED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:17:13-36
	android:configChanges
		ADDED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:18:13-74
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:16:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER+category:name:android.intent.category.LEANBACK_LAUNCHER
ADDED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:19:13-23:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:20:17-69
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:20:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:21:17-77
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:21:27-74
category#android.intent.category.LEANBACK_LAUNCHER
ADDED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:22:17-86
	android:name
		ADDED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:22:27-83
uses-sdk
INJECTED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml
MERGED from [ijkplayer-cmake-release.aar] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\ca9f848606dba5a7c7d8175588849d6d\transformed\ijkplayer-cmake-release\AndroidManifest.xml:5:5-44
MERGED from [ijkplayer-cmake-release.aar] C:\Users\<USER>\.gradle\caches\9.0-milestone-1\transforms\ca9f848606dba5a7c7d8175588849d6d\transformed\ijkplayer-cmake-release\AndroidManifest.xml:5:5-44
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml

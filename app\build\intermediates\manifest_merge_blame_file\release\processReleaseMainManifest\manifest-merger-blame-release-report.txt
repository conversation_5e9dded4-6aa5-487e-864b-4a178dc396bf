1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.linkeye" >
4
5    <uses-sdk
6        android:minSdkVersion="23"
7        android:targetSdkVersion="35" />
8
9    <uses-permission android:name="android.permission.INTERNET" />
9-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:5:5-67
9-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:5:22-64
10
11    <uses-feature
11-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:6:5-87
12        android:name="android.software.leanback"
12-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:6:19-59
13        android:required="false" />
13-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:6:60-84
14    <uses-feature
14-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:7:5-90
15        android:name="android.hardware.touchscreen"
15-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:7:19-62
16        android:required="false" />
16-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:7:63-87
17
18    <application
18-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:9:5-25:19
19        android:allowBackup="true"
19-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:11:9-35
20        android:extractNativeLibs="false"
21        android:hardwareAccelerated="true"
21-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:12:9-43
22        android:label="link-eye"
22-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:10:9-33
23        android:networkSecurityConfig="@xml/network_security_config"
23-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:13:9-69
24        android:usesCleartextTraffic="true" >
24-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:14:9-44
25        <activity
25-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:15:9-24:20
26            android:name="com.example.linkeye.MainActivity"
26-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:16:13-41
27            android:configChanges="keyboardHidden|orientation|screenSize"
27-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:18:13-74
28            android:exported="true" >
28-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:17:13-36
29            <intent-filter>
29-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:19:13-23:29
30                <action android:name="android.intent.action.MAIN" />
30-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:20:17-69
30-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:20:25-66
31
32                <category android:name="android.intent.category.LAUNCHER" />
32-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:21:17-77
32-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:21:27-74
33                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
33-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:22:17-86
33-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:22:27-83
34            </intent-filter>
35        </activity>
36    </application>
37
38</manifest>

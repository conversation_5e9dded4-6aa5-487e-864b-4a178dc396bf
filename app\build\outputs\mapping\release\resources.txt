android.content.res.Resources#getIdentifier present: true
Web content present: false
Referenced Strings:
omx.ffmpeg.
tv.danmaku.ijk.media.player.IjkMediaP...
mime
Extended
OMX.ST.VFM.H264Dec
rtsp_transport
ijkplayer
tv_prefs
Unknown
该分组没有频道
OMX.IMG.MSVDX.Decoder.AVC
height
11
12
13
IjkMediaCodecInfo
raw
rtsp
OMX.Intel.hw_vd.h264
codec_name
OMX.Nvidia.h264.decode
0
1
2
3
4
omx.mtk.
5
codec_level
4路
OMX.k3.ffmpeg.decoder
21
22
OMX.Intel.VideoDecoder.AVC
1b
reconnect
OMX.Action.Video.Decoder
sar_den
audio
tv_mode
31
32
OMX.RENESAS.VIDEO.DECODER.H264
选择频道分组
fps_num
r
OMX.hantro.81x0.video.decoder
取消
空
OMX.cosmo.video.decoder.avc
41
OMX.Exynos.avc.dec
42
OMX.vpu.video_decoder.avc
OMX.TI.DUCATI1.VIDEO.DECODER
channel_layout
com.example.linkeye
bitrate
language
tbr_num
OMX.WMT.decoder.avc
timedtext
m3u8_prefs
loop
OMX.SEC.avc.sw.dec
codec_pixel_format
51
52
OMX.sprd.h264.decoder
电视模式
retry_counter
duration_us
输入M3U8播放列表URL
正在测试连接...
assign_
port
name
http
page
fd
静音开关
N/A
置空
OMX.bluestacks.hw.decoder
android
OMX.LG.decoder.video.avc
video
timeout
content
mono
OMX.MARVELL.VIDEO.H264DECODER
omx.google.
OMX.MS.AVC.Decoder
headers
确定
rtsp_flags
omx.ittiam.
OMX.ffmpeg.video.decoder
und
fps_den
OMX.Nvidia.h264.decode.secure
没有可用的电视频道
%x
设置M3U8地址
family
OMX.BRCM.vc4.decoder.avc
release
type
HW
tbr_den
codec_profile
OMX.hisi.video.decoder
OMX.hantro.G1.video.decoder
Extends
codec_long_name
OMX.qcom.video.decoder.avc
omx.
streams
ip
Baseline
stereo
GET
h264
libijkplayer.so
https://ipvv.nobug.cc/tv/rtsp.m3u8
OMX.google.h264.lc.decoder
%02d:%02d:%02d
测试连接
OMX.MARVELL.VIDEO.HW.CODA7542DECODER
protocol_whitelist
unknown
mute_
rtmp
cameras
file
power
muted
OMX.duos.h264.decoder
设置
OMX.rk.video_decoder.avc
mediacodec
url
file_size
OMX.sprd.soft.h264.decoder
sample_rate
grid
width
High444
codec_profile_id
OMX.SEC.AVC.Decoder
OMX.RTK.video.decoder
segment_index
settings
OMX.SEC.avc.dec
offset
未分组
OMX.amlogic.avc.decoder.awesome
format
OMX.google.h264.decoder
omx.avcodec.
null
High10
OMX.brcm.video.h264.decoder
选择摄像头
High422
omx.pv
udp
OMX.SEC.avcdec
OMX.brcm.video.h264.hw.decoder
prefer_tcp
error
OMX.allwinner.video.decoder.avc
SUBTITLE
sar_num
omx.k3.ffmpeg.
OMX.k3.video.decoder.avc
1路
m3u8_url
OMX.Exynos.AVC.Decoder
High
tcp
start_us
SSL
OMX.ittiam.video.decoder.avc
http_code
UNKNOWN
Main
2路
Marking id:mute_badge:2130837507 used because it matches string pool constant mute_
Marking raw:cameras:2130968576 used because it matches string pool constant cameras
@drawable/ic_mute : reachable=false
@id/focus_border : reachable=true
@id/grid_container : reachable=true
@id/loading_indicator : reachable=true
@id/mute_badge : reachable=true
@id/surface_view : reachable=true
@id/tile_root : reachable=false
@id/title : reachable=true
@layout/activity_main : reachable=true
@layout/player_tile : reachable=true
    @drawable/ic_mute
@raw/cameras : reachable=true
@xml/network_security_config : reachable=true

The root reachable resources are:
 id:focus_border:2130837504
 id:grid_container:2130837505
 id:loading_indicator:2130837506
 id:mute_badge:2130837507
 id:surface_view:2130837508
 id:title:2130837510
 layout:activity_main:2130903040
 layout:player_tile:2130903041
 raw:cameras:2130968576
 xml:network_security_config:2131034112
Unused resources are: 
 id:tile_root:2130837509
Resource shrinking did not work (grew from 4572 to 4692); using original instead

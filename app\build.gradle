plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
}

android {
    namespace "com.example.linkeye"
    compileSdkVersion 35

    defaultConfig {
        applicationId "com.example.linkeye"
        minSdkVersion 23
        targetSdkVersion 35
        versionCode getVersionCode()
        versionName getVersionName()

        ndk {
            abiFilters 'armeabi-v7a', 'arm64-v8a', 'x86'
        }

        buildConfigField "String", "BUILD_TIME", "\"${getBuildTime()}\""
        buildConfigField "String", "GIT_COMMIT", "\"${getGitCommit()}\""
    }

    signingConfigs {
        release {
            storeFile file('release-key.keystore')
            storePassword 'linkeye123'
            keyAlias 'linkeye-release'
            keyPassword 'linkeye123'
        }
    }

    buildTypes {
        release {
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release
            debuggable false
            jniDebuggable false
            renderscriptDebuggable false
            zipAlignEnabled true
        }
        debug {
            debuggable true
            applicationIdSuffix ".debug"
            versionNameSuffix "-debug"
        }
    }

    buildFeatures {
        buildConfig true
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    packagingOptions {
        exclude 'META-INF/LICENSE'
        exclude 'META-INF/NOTICE'
    }
    ndkVersion '27.0.12077973'
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation fileTree(dir: 'libs', include: ['*.aar'])
//    implementation(name: 'ijkplayer-cmake-release', ext: 'aar')
}

// 版本管理函数
def getVersionCode() {
    def versionPropsFile = file('version.properties')
    if (versionPropsFile.exists()) {
        def Properties versionProps = new Properties()
        versionProps.load(new FileInputStream(versionPropsFile))
        return versionProps['VERSION_CODE'].toInteger()
    } else {
        return 1
    }
}

def getVersionName() {
    def versionPropsFile = file('version.properties')
    if (versionPropsFile.exists()) {
        def Properties versionProps = new Properties()
        versionProps.load(new FileInputStream(versionPropsFile))
        return versionProps['VERSION_NAME']
    } else {
        return "1.0.0"
    }
}

def getBuildTime() {
    return new Date().format("yyyy-MM-dd HH:mm:ss")
}

def getGitCommit() {
    try {
        def gitDir = file('.git')
        if (!gitDir.exists()) {
            return "no-git"
        }
        def stdout = new ByteArrayOutputStream()
        exec {
            commandLine 'git', 'rev-parse', '--short', 'HEAD'
            standardOutput = stdout
        }
        return stdout.toString().trim()
    } catch (Exception e) {
        return "unknown"
    }
}
